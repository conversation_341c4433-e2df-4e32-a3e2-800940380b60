@extends('layouts.app')

@section('title', 'Visual Impairment Solutions: Inclusive Opportunities and navigation for English Learning')

@section('content')
<!-- Hero Section with VISION -->
<section class="hero-bg">
    <div class="container">
        <div class="row justify-content-center text-center text-white">
            <div class="col-lg-10">
                <h1 class="display-1 fw-bold mb-4 text-gradient" style="font-size: 6rem; letter-spacing: 0.2em;">
                    VISION
                </h1>
                <p class="fs-3 mb-4 fw-light">
                    English for Young Learner
                </p>
                <p class="fs-5 mb-5 opacity-90">
                    Visual Impairment Solutions: Inclusive Opportunities and navigation for English Learning
                </p>

                <!-- Search Bar -->
                <div class="row justify-content-center mb-5">
                    <div class="col-md-6">
                        <div class="input-group input-group-lg">
                            <input type="text" class="form-control" placeholder="Search courses..."
                                   style="border-radius: 50px 0 0 50px;">
                            <button class="btn btn-edunetra" type="button" style="border-radius: 0 50px 50px 0;">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Course Management Button for Teachers -->
                @auth
                    @if(auth()->user()->isTeacher() || auth()->user()->isAdmin())
                        <div class="mb-4">
                            <a href="#" class="btn btn-edunetra btn-lg">
                                <i class="bi bi-gear-fill me-2"></i> Course Management
                            </a>
                        </div>
                    @endif
                @endauth

                <!-- Scroll Down Indicator -->
                <div class="mt-5">
                    <a href="#sessions" class="text-white text-decoration-none">
                        <i class="bi bi-chevron-down fs-2 animate-bounce"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Session Navigation -->
<section id="sessions" class="bg-edunetra-yellow py-4">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="d-flex flex-wrap justify-content-center gap-3">
                    <a href="#" class="btn btn-light fw-semibold">PENDAHULUAN</a>
                    @for($i = 1; $i <= 8; $i++)
                        <a href="#" class="btn btn-light fw-semibold">SESI {{ $i }}</a>
                    @endfor
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Courses Section -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-4 fw-bold text-edunetra-dark mb-3">Featured Courses</h2>
                <p class="fs-5 text-muted">Discover our most popular learning programs</p>
            </div>
        </div>

        <div class="row g-4">
            @foreach($featuredCourses as $course)
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow">
                        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                            @if($course->image)
                                <img src="{{ $course->image }}" alt="{{ $course->title }}" class="img-fluid">
                            @else
                                <div class="text-center text-muted">
                                    <i class="bi bi-book fs-1 mb-2"></i>
                                    <p class="mb-0">{{ $course->title }}</p>
                                </div>
                            @endif
                        </div>
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title fw-bold">{{ $course->title }}</h5>
                            <p class="card-text text-muted flex-grow-1">{{ Str::limit($course->description, 100) }}</p>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <small class="text-muted">
                                    <i class="bi bi-person"></i> {{ $course->teacher->name }}
                                </small>
                                <span class="badge bg-edunetra-yellow text-dark">
                                    {{ ucfirst($course->level) }}
                                </span>
                            </div>

                            <div class="d-flex justify-content-between align-items-center">
                                <span class="h5 mb-0 fw-bold">
                                    @if($course->price > 0)
                                        Rp {{ number_format($course->price, 0, ',', '.') }}
                                    @else
                                        <span class="text-success">Free</span>
                                    @endif
                                </span>
                                <a href="{{ route('courses.show', $course->slug) }}"
                                   class="btn btn-edunetra">
                                    View Course
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <div class="row mt-5">
            <div class="col-12 text-center">
                <a href="{{ route('courses.public') }}"
                   class="btn btn-outline-dark btn-lg">
                    <i class="bi bi-grid me-2"></i> View All Courses
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Announcements Section -->
<section class="section-padding bg-white">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5">
                <h2 class="display-4 fw-bold text-edunetra-dark mb-3">Latest Announcements</h2>
                <p class="fs-5 text-muted">Stay updated with our latest news and opportunities</p>
            </div>
        </div>

        <div class="row g-4 mb-5">
            @foreach($announcements as $announcement)
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-3">
                                <span class="badge bg-edunetra-yellow text-dark me-2">
                                    {{ ucwords(str_replace('_', ' ', $announcement->type)) }}
                                </span>
                                @if($announcement->is_featured)
                                    <i class="bi bi-star-fill text-warning"></i>
                                @endif
                            </div>
                            <h5 class="card-title fw-bold">{{ $announcement->title }}</h5>
                            <p class="card-text text-muted">{{ Str::limit($announcement->content, 120) }}</p>
                            <small class="text-muted">
                                <i class="bi bi-calendar"></i> {{ $announcement->published_at->format('M d, Y') }}
                            </small>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <!-- Special Programs -->
        <div class="row g-4 mb-5">
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 bg-primary bg-opacity-10 h-100 text-center">
                    <div class="card-body">
                        <i class="bi bi-info-circle fs-1 text-primary mb-3"></i>
                        <h5 class="card-title fw-bold text-primary">New Info</h5>
                        <p class="card-text text-muted">Latest updates and information</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 bg-success bg-opacity-10 h-100 text-center">
                    <div class="card-body">
                        <i class="bi bi-people fs-1 text-success mb-3"></i>
                        <h5 class="card-title fw-bold text-success">Volunteer</h5>
                        <p class="card-text text-muted">Join our volunteer programs</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 bg-warning bg-opacity-10 h-100 text-center">
                    <div class="card-body">
                        <i class="bi bi-briefcase fs-1 text-warning mb-3"></i>
                        <h5 class="card-title fw-bold text-warning">Open Recruitment</h5>
                        <p class="card-text text-muted">Career opportunities</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 bg-info bg-opacity-10 h-100 text-center">
                    <div class="card-body">
                        <i class="bi bi-person-workspace fs-1 text-info mb-3"></i>
                        <h5 class="card-title fw-bold text-info">Shadow Teacher</h5>
                        <p class="card-text text-muted">Teaching experience program</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- LENTERA Program -->
        <div class="row">
            <div class="col-12">
                <div class="card border-0 bg-gradient text-white text-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <div class="card-body py-5">
                        <h3 class="display-5 fw-bold mb-3">LENTERA</h3>
                        <p class="fs-4 mb-3">Layanan Pendampingan Mobilitas Difabel Netra</p>
                        <p class="fs-6 mb-4 opacity-90">Specialized support services for visually impaired learners</p>
                        <a href="#" class="btn btn-light btn-lg">
                            <i class="bi bi-arrow-right me-2"></i> Learn More
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
